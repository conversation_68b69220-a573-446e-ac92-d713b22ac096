using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 保养模板服务实现
    /// </summary>
    public partial class MaintenanceTemplateService : IMaintenanceTemplateService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<MaintenanceTemplateService> _logger;

        public MaintenanceTemplateService(DatabaseContext dbContext, ILogger<MaintenanceTemplateService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<MaintenanceTemplateDto>> GetMaintenanceTemplatesAsync(
            string? keyword = null,
            int? componentCategoryId = null,
            int? maintenanceType = null,
            bool? isEnabled = null)
        {
            try
            {
                // 简化查询，直接获取MaintenanceTemplate数据
                var query = _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .Where(mt => true);

                // 应用筛选条件
                if (!string.IsNullOrWhiteSpace(keyword))
                {
                    query = query.Where(mt =>
                        mt.Name.Contains(keyword) ||
                        (mt.Description != null && mt.Description.Contains(keyword)));
                }

                if (componentCategoryId.HasValue)
                {
                    query = query.Where(mt => mt.ComponentCategoryId == componentCategoryId.Value);
                }

                if (maintenanceType.HasValue)
                {
                    query = query.Where(mt => mt.MaintenanceType == maintenanceType.Value);
                }

                if (isEnabled.HasValue)
                {
                    query = query.Where(mt => mt.IsEnabled == isEnabled.Value);
                }

                // 获取基础数据
                var baseTemplates = await query.ToListAsync();

                // 转换为DTO
                var templates = baseTemplates.Select(mt => new MaintenanceTemplateDto
                {
                    Id = mt.Id,
                    Name = mt.Name,
                    ComponentCategoryId = mt.ComponentCategoryId,
                    ComponentCategoryName = null, // 暂时为空，后续可以优化
                    ComponentId = mt.ComponentId,
                    ComponentName = null, // 暂时为空，后续可以优化
                    MaintenanceType = mt.MaintenanceType,
                    CycleType = mt.CycleType,
                    CycleDays = mt.CycleDays,
                    CycleUsageHours = mt.CycleUsageHours,
                    CycleUsageCount = mt.CycleUsageCount,
                    EstimatedDurationMinutes = mt.EstimatedDurationMinutes,
                    RequiredSkillLevel = mt.RequiredSkillLevel,
                    Description = mt.Description,
                    Instructions = mt.Instructions,
                    IsEnabled = mt.IsEnabled,
                    CreatedAt = mt.CreatedAt,
                    CreatedByName = null, // 暂时为空，后续可以优化
                    UpdatedAt = mt.UpdatedAt,
                    Remark = mt.Remark
                }).ToList();

                // 获取每个模板的检查项目统计并设置计算属性
                foreach (var template in templates)
                {
                    var checkItemStats = await _dbContext.Db.Queryable<MaintenanceCheckItem>()
                        .Where(ci => ci.TemplateId == template.Id && ci.IsEnabled)
                        .GroupBy(ci => ci.IsRequired)
                        .Select(g => new { IsRequired = g.IsRequired, Count = SqlFunc.AggregateCount(g.Id) })
                        .ToListAsync();

                    template.CheckItemCount = checkItemStats.Sum(s => s.Count);
                    template.RequiredItemCount = checkItemStats.Where(s => s.IsRequired).Sum(s => s.Count);
                    template.OptionalItemCount = checkItemStats.Where(s => !s.IsRequired).Sum(s => s.Count);

                    // 设置计算属性
                    template.MaintenanceTypeName = template.MaintenanceType switch
                    {
                        1 => "日常保养",
                        2 => "定期保养",
                        3 => "大修保养",
                        _ => "未知"
                    };

                    template.CycleTypeName = template.CycleType switch
                    {
                        1 => "按时间",
                        2 => "按使用量",
                        _ => "未知"
                    };

                    template.SkillLevelName = template.RequiredSkillLevel switch
                    {
                        1 => "初级",
                        2 => "中级",
                        3 => "高级",
                        _ => "未知"
                    };

                    template.CycleDescription = template.CycleType switch
                    {
                        1 => template.CycleDays.HasValue ? $"{template.CycleDays}天" : "未设置",
                        2 => template.CycleUsageHours.HasValue ? $"{template.CycleUsageHours}小时" :
                             template.CycleUsageCount.HasValue ? $"{template.CycleUsageCount}次" : "未设置",
                        _ => "未知"
                    };

                    template.DurationDescription = template.EstimatedDurationMinutes < 60
                        ? $"{template.EstimatedDurationMinutes}分钟"
                        : template.EstimatedDurationMinutes % 60 == 0
                            ? $"{template.EstimatedDurationMinutes / 60}小时"
                            : $"{template.EstimatedDurationMinutes / 60}小时{template.EstimatedDurationMinutes % 60}分钟";

                    template.MaintenanceTypeColor = template.MaintenanceType switch
                    {
                        1 => "info",
                        2 => "warning",
                        3 => "error",
                        _ => "default"
                    };

                    template.SkillLevelColor = template.RequiredSkillLevel switch
                    {
                        1 => "success",
                        2 => "warning",
                        3 => "error",
                        _ => "default"
                    };
                }

                return templates.OrderBy(t => t.MaintenanceType).ThenBy(t => t.Name).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取保养模板列表失败");
                throw;
            }
        }

        public async Task<MaintenanceTemplateDto?> GetMaintenanceTemplateByIdAsync(int id)
        {
            try
            {
                var template = await _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .LeftJoin<ComponentCategory>((mt, cc) => mt.ComponentCategoryId == cc.Id)
                    .LeftJoin<Component>((mt, cc, c) => mt.ComponentId == c.Id)
                    .LeftJoin<User>((mt, cc, c, u) => mt.CreatedBy == u.Id)
                    .Where((mt, cc, c, u) => mt.Id == id)
                    .Select((mt, cc, c, u) => new MaintenanceTemplateDto
                    {
                        Id = mt.Id,
                        Name = mt.Name,
                        ComponentCategoryId = mt.ComponentCategoryId,
                        ComponentCategoryName = cc.Name,
                        ComponentId = mt.ComponentId,
                        ComponentName = c.Name,
                        MaintenanceType = mt.MaintenanceType,
                        CycleType = mt.CycleType,
                        CycleDays = mt.CycleDays,
                        CycleUsageHours = mt.CycleUsageHours,
                        CycleUsageCount = mt.CycleUsageCount,
                        EstimatedDurationMinutes = mt.EstimatedDurationMinutes,
                        RequiredSkillLevel = mt.RequiredSkillLevel,
                        Description = mt.Description,
                        Instructions = mt.Instructions,
                        IsEnabled = mt.IsEnabled,
                        CreatedAt = mt.CreatedAt,
                        CreatedByName = u.DisplayName,
                        UpdatedAt = mt.UpdatedAt,
                        Remark = mt.Remark
                    }).FirstAsync();

                if (template != null)
                {
                    // 获取检查项目统计
                    var checkItemStats = await _dbContext.Db.Queryable<MaintenanceCheckItem>()
                        .Where(ci => ci.TemplateId == template.Id && ci.IsEnabled)
                        .GroupBy(ci => ci.IsRequired)
                        .Select(g => new { IsRequired = g.IsRequired, Count = SqlFunc.AggregateCount(g.Id) })
                        .ToListAsync();

                    template.CheckItemCount = checkItemStats.Sum(s => s.Count);
                    template.RequiredItemCount = checkItemStats.Where(s => s.IsRequired).Sum(s => s.Count);
                    template.OptionalItemCount = checkItemStats.Where(s => !s.IsRequired).Sum(s => s.Count);

                    // 设置计算属性
                    template.MaintenanceTypeName = template.MaintenanceType switch
                    {
                        1 => "日常保养",
                        2 => "定期保养",
                        3 => "大修保养",
                        _ => "未知"
                    };

                    template.CycleTypeName = template.CycleType switch
                    {
                        1 => "按时间",
                        2 => "按使用量",
                        _ => "未知"
                    };

                    template.SkillLevelName = template.RequiredSkillLevel switch
                    {
                        1 => "初级",
                        2 => "中级",
                        3 => "高级",
                        _ => "未知"
                    };

                    template.CycleDescription = template.CycleType switch
                    {
                        1 => template.CycleDays.HasValue ? $"{template.CycleDays}天" : "未设置",
                        2 => template.CycleUsageHours.HasValue ? $"{template.CycleUsageHours}小时" :
                             template.CycleUsageCount.HasValue ? $"{template.CycleUsageCount}次" : "未设置",
                        _ => "未知"
                    };

                    template.DurationDescription = template.EstimatedDurationMinutes < 60
                        ? $"{template.EstimatedDurationMinutes}分钟"
                        : template.EstimatedDurationMinutes % 60 == 0
                            ? $"{template.EstimatedDurationMinutes / 60}小时"
                            : $"{template.EstimatedDurationMinutes / 60}小时{template.EstimatedDurationMinutes % 60}分钟";

                    template.MaintenanceTypeColor = template.MaintenanceType switch
                    {
                        1 => "info",
                        2 => "warning",
                        3 => "error",
                        _ => "default"
                    };

                    template.SkillLevelColor = template.RequiredSkillLevel switch
                    {
                        1 => "success",
                        2 => "warning",
                        3 => "error",
                        _ => "default"
                    };
                }

                return template;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取保养模板详情失败: {templateId}", id);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int? TemplateId)> CreateMaintenanceTemplateAsync(MaintenanceTemplateCreateDto createDto)
        {
            try
            {
                // 验证模板配置
                var validation = await ValidateTemplateAsync(createDto);
                if (!validation.IsValid)
                {
                    return (false, string.Join("; ", validation.ErrorMessages), null);
                }

                // 检查模板名称是否重复
                var existingTemplate = await _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .Where(mt => mt.Name == createDto.Name && mt.IsEnabled)
                    .FirstAsync();

                if (existingTemplate != null)
                {
                    return (false, "模板名称已存在", null);
                }

                // 开始事务
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 创建模板
                    var template = new MaintenanceTemplate
                    {
                        Name = createDto.Name,
                        ComponentCategoryId = createDto.ComponentCategoryId,
                        ComponentId = createDto.ComponentId,
                        MaintenanceType = createDto.MaintenanceType,
                        CycleType = createDto.CycleType,
                        CycleDays = createDto.CycleDays,
                        CycleUsageHours = createDto.CycleUsageHours,
                        CycleUsageCount = createDto.CycleUsageCount,
                        EstimatedDurationMinutes = createDto.EstimatedDurationMinutes,
                        RequiredSkillLevel = createDto.RequiredSkillLevel,
                        Description = createDto.Description,
                        Instructions = createDto.Instructions,
                        Remark = createDto.Remark,
                        IsEnabled = true,
                        CreatedAt = DateTime.Now
                    };

                    var templateId = await _dbContext.Db.Insertable(template).ExecuteReturnIdentityAsync();

                    // 创建检查项目
                    if (createDto.CheckItems.Any())
                    {
                        var checkItems = createDto.CheckItems.Select(ci => new MaintenanceCheckItem
                        {
                            TemplateId = templateId,
                            ItemName = ci.ItemName,
                            ItemType = ci.ItemType,
                            CheckMethod = ci.CheckMethod,
                            StandardValue = ci.StandardValue,
                            ToleranceRange = ci.ToleranceRange,
                            IsRequired = ci.IsRequired,
                            SortOrder = ci.SortOrder,
                            Description = ci.Description,
                            Remark = ci.Remark,
                            IsEnabled = true,
                            CreatedAt = DateTime.Now
                        }).ToList();

                        await _dbContext.Db.Insertable(checkItems).ExecuteCommandAsync();
                    }

                    return templateId;
                });

                _logger.LogInformation("成功创建保养模板: {templateName}, ID: {templateId}", createDto.Name, result.Data);
                return (true, "创建成功", result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建保养模板失败: {templateName}", createDto.Name);
                return (false, $"创建失败: {ex.Message}", null);
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateMaintenanceTemplateAsync(int id, MaintenanceTemplateCreateDto updateDto)
        {
            try
            {
                // 验证模板配置
                var validation = await ValidateTemplateAsync(updateDto);
                if (!validation.IsValid)
                {
                    return (false, string.Join("; ", validation.ErrorMessages));
                }

                // 检查模板是否存在
                var existingTemplate = await _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .Where(mt => mt.Id == id)
                    .FirstAsync();

                if (existingTemplate == null)
                {
                    return (false, "模板不存在");
                }

                // 检查名称是否与其他模板重复
                var duplicateTemplate = await _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .Where(mt => mt.Name == updateDto.Name && mt.Id != id && mt.IsEnabled)
                    .FirstAsync();

                if (duplicateTemplate != null)
                {
                    return (false, "模板名称已存在");
                }

                // 开始事务更新
                await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 更新模板基本信息
                    existingTemplate.Name = updateDto.Name;
                    existingTemplate.ComponentCategoryId = updateDto.ComponentCategoryId;
                    existingTemplate.ComponentId = updateDto.ComponentId;
                    existingTemplate.MaintenanceType = updateDto.MaintenanceType;
                    existingTemplate.CycleType = updateDto.CycleType;
                    existingTemplate.CycleDays = updateDto.CycleDays;
                    existingTemplate.CycleUsageHours = updateDto.CycleUsageHours;
                    existingTemplate.CycleUsageCount = updateDto.CycleUsageCount;
                    existingTemplate.EstimatedDurationMinutes = updateDto.EstimatedDurationMinutes;
                    existingTemplate.RequiredSkillLevel = updateDto.RequiredSkillLevel;
                    existingTemplate.Description = updateDto.Description;
                    existingTemplate.Instructions = updateDto.Instructions;
                    existingTemplate.Remark = updateDto.Remark;
                    existingTemplate.UpdatedAt = DateTime.Now;

                    await _dbContext.Db.Updateable(existingTemplate).ExecuteCommandAsync();

                    // 删除现有检查项目
                    await _dbContext.Db.Deleteable<MaintenanceCheckItem>()
                        .Where(ci => ci.TemplateId == id)
                        .ExecuteCommandAsync();

                    // 重新创建检查项目
                    if (updateDto.CheckItems.Any())
                    {
                        var checkItems = updateDto.CheckItems.Select(ci => new MaintenanceCheckItem
                        {
                            TemplateId = id,
                            ItemName = ci.ItemName,
                            ItemType = ci.ItemType,
                            CheckMethod = ci.CheckMethod,
                            StandardValue = ci.StandardValue,
                            ToleranceRange = ci.ToleranceRange,
                            IsRequired = ci.IsRequired,
                            SortOrder = ci.SortOrder,
                            Description = ci.Description,
                            Remark = ci.Remark,
                            IsEnabled = true,
                            CreatedAt = DateTime.Now
                        }).ToList();

                        await _dbContext.Db.Insertable(checkItems).ExecuteCommandAsync();
                    }
                });

                _logger.LogInformation("成功更新保养模板: {templateName}, ID: {templateId}", updateDto.Name, id);
                return (true, "更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新保养模板失败: {templateId}", id);
                return (false, $"更新失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteMaintenanceTemplateAsync(int id)
        {
            try
            {
                // 检查模板是否存在
                var template = await _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .Where(mt => mt.Id == id)
                    .FirstAsync();

                if (template == null)
                {
                    return (false, "模板不存在");
                }

                // 检查是否有关联的保养任务
                var hasRelatedTasks = await _dbContext.Db.Queryable<MaintenanceTask>()
                    .Where(mt => mt.TemplateId == id)
                    .AnyAsync();

                if (hasRelatedTasks)
                {
                    return (false, "该模板已被保养任务使用，无法删除");
                }

                // 开始事务删除
                await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 删除检查项目
                    await _dbContext.Db.Deleteable<MaintenanceCheckItem>()
                        .Where(ci => ci.TemplateId == id)
                        .ExecuteCommandAsync();

                    // 删除模板
                    await _dbContext.Db.Deleteable<MaintenanceTemplate>()
                        .Where(mt => mt.Id == id)
                        .ExecuteCommandAsync();
                });

                _logger.LogInformation("成功删除保养模板: {templateName}, ID: {templateId}", template.Name, id);
                return (true, "删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除保养模板失败: {templateId}", id);
                return (false, $"删除失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleMaintenanceTemplateStatusAsync(int id, bool isEnabled)
        {
            try
            {
                var result = await _dbContext.Db.Updateable<MaintenanceTemplate>()
                    .SetColumns(mt => new MaintenanceTemplate { IsEnabled = isEnabled, UpdatedAt = DateTime.Now })
                    .Where(mt => mt.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("成功{action}保养模板: ID {templateId}", isEnabled ? "启用" : "禁用", id);
                    return (true, $"{(isEnabled ? "启用" : "禁用")}成功");
                }
                else
                {
                    return (false, "模板不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{action}保养模板失败: {templateId}", isEnabled ? "启用" : "禁用", id);
                return (false, $"{(isEnabled ? "启用" : "禁用")}失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int? NewTemplateId)> CopyMaintenanceTemplateAsync(int sourceId, string newName)
        {
            try
            {
                // 获取源模板
                var sourceTemplate = await _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .Where(mt => mt.Id == sourceId)
                    .FirstAsync();

                if (sourceTemplate == null)
                {
                    return (false, "源模板不存在", null);
                }

                // 检查新名称是否重复
                var existingTemplate = await _dbContext.Db.Queryable<MaintenanceTemplate>()
                    .Where(mt => mt.Name == newName && mt.IsEnabled)
                    .FirstAsync();

                if (existingTemplate != null)
                {
                    return (false, "模板名称已存在", null);
                }

                // 获取源模板的检查项目
                var sourceCheckItems = await _dbContext.Db.Queryable<MaintenanceCheckItem>()
                    .Where(ci => ci.TemplateId == sourceId && ci.IsEnabled)
                    .ToListAsync();

                // 开始事务复制
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 创建新模板
                    var newTemplate = new MaintenanceTemplate
                    {
                        Name = newName,
                        ComponentCategoryId = sourceTemplate.ComponentCategoryId,
                        ComponentId = sourceTemplate.ComponentId,
                        MaintenanceType = sourceTemplate.MaintenanceType,
                        CycleType = sourceTemplate.CycleType,
                        CycleDays = sourceTemplate.CycleDays,
                        CycleUsageHours = sourceTemplate.CycleUsageHours,
                        CycleUsageCount = sourceTemplate.CycleUsageCount,
                        EstimatedDurationMinutes = sourceTemplate.EstimatedDurationMinutes,
                        RequiredSkillLevel = sourceTemplate.RequiredSkillLevel,
                        Description = sourceTemplate.Description,
                        Instructions = sourceTemplate.Instructions,
                        Remark = $"复制自: {sourceTemplate.Name}",
                        IsEnabled = true,
                        CreatedAt = DateTime.Now
                    };

                    var templateId = await _dbContext.Db.Insertable(newTemplate).ExecuteReturnIdentityAsync();

                    // 复制检查项目
                    if (sourceCheckItems.Any())
                    {
                        var newCheckItems = sourceCheckItems.Select(ci => new MaintenanceCheckItem
                        {
                            TemplateId = templateId,
                            ItemName = ci.ItemName,
                            ItemType = ci.ItemType,
                            CheckMethod = ci.CheckMethod,
                            StandardValue = ci.StandardValue,
                            ToleranceRange = ci.ToleranceRange,
                            IsRequired = ci.IsRequired,
                            SortOrder = ci.SortOrder,
                            Description = ci.Description,
                            Remark = ci.Remark,
                            IsEnabled = true,
                            CreatedAt = DateTime.Now
                        }).ToList();

                        await _dbContext.Db.Insertable(newCheckItems).ExecuteCommandAsync();
                    }

                    return templateId;
                });

                _logger.LogInformation("成功复制保养模板: 源模板 {sourceId} -> 新模板 {newTemplateId} ({newName})",
                    sourceId, result.Data, newName);
                return (true, "复制成功", result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制保养模板失败: 源模板 {sourceId}", sourceId);
                return (false, $"复制失败: {ex.Message}", null);
            }
        }

        // 暂时使用简化实现，完整实现将在后续添加
        public async Task<List<MaintenanceCheckItemDto>> GetTemplateCheckItemsAsync(int templateId)
        {
            var checkItems = await _dbContext.Db.Queryable<MaintenanceCheckItem>()
                .Where(ci => ci.TemplateId == templateId && ci.IsEnabled)
                .OrderBy(ci => ci.SortOrder)
                .ToListAsync();

            return checkItems.Select(ci => new MaintenanceCheckItemDto
            {
                Id = ci.Id,
                TemplateId = ci.TemplateId,
                ItemName = ci.ItemName,
                ItemType = ci.ItemType,
                CheckMethod = ci.CheckMethod,
                StandardValue = ci.StandardValue,
                ToleranceRange = ci.ToleranceRange,
                IsRequired = ci.IsRequired,
                SortOrder = ci.SortOrder,
                Description = ci.Description,
                IsEnabled = ci.IsEnabled,
                Notes = ci.Remark
            }).ToList();
        }

        public Task<(bool IsSuccess, string ErrorMessage, int? ItemId)> AddCheckItemToTemplateAsync(int templateId, MaintenanceCheckItemCreateDto checkItem)
        {
            throw new NotImplementedException("将在第二阶段实现");
        }

        public Task<(bool IsSuccess, string ErrorMessage)> UpdateCheckItemAsync(int itemId, MaintenanceCheckItemCreateDto checkItem)
        {
            throw new NotImplementedException("将在第二阶段实现");
        }

        public Task<(bool IsSuccess, string ErrorMessage)> DeleteCheckItemAsync(int itemId)
        {
            throw new NotImplementedException("将在第二阶段实现");
        }

        public Task<(bool IsSuccess, string ErrorMessage)> UpdateCheckItemOrdersAsync(int templateId, Dictionary<int, int> itemOrders)
        {
            throw new NotImplementedException("将在第二阶段实现");
        }

        public async Task<List<MaintenanceTemplateDto>> GetApplicableTemplatesAsync(int? componentId = null, int? componentCategoryId = null)
        {
            return await GetMaintenanceTemplatesAsync();
        }

        public async Task<(bool IsValid, List<string> ErrorMessages)> ValidateTemplateAsync(MaintenanceTemplateCreateDto createDto)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(createDto.Name))
                errors.Add("模板名称不能为空");

            if (createDto.EstimatedDurationMinutes <= 0)
                errors.Add("预计保养时长必须大于0");

            return (errors.Count == 0, errors);
        }

        public async Task<MaintenanceTemplateStatisticsDto> GetTemplateStatisticsAsync()
        {
            var templates = await _dbContext.Db.Queryable<MaintenanceTemplate>().ToListAsync();

            return new MaintenanceTemplateStatisticsDto
            {
                TotalTemplates = templates.Count,
                EnabledTemplates = templates.Count(t => t.IsEnabled),
                DisabledTemplates = templates.Count(t => !t.IsEnabled),
                DailyMaintenanceTemplates = templates.Count(t => t.MaintenanceType == 1),
                PeriodicMaintenanceTemplates = templates.Count(t => t.MaintenanceType == 2),
                OverhaulMaintenanceTemplates = templates.Count(t => t.MaintenanceType == 3),
                GeneralTemplates = templates.Count(t => t.ComponentId == null && t.ComponentCategoryId == null),
                SpecificTemplates = templates.Count(t => t.ComponentId != null || t.ComponentCategoryId != null),
                AverageCheckItems = 0,
                AverageDurationMinutes = templates.Count > 0 ? templates.Average(t => t.EstimatedDurationMinutes) : 0
            };
        }
    }
}
