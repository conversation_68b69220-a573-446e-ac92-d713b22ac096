using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 保养模板表
    /// </summary>
    [SugarTable("MaintenanceTemplates")]
    public class MaintenanceTemplate
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Required, MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类ID（可选，用于通用模板）
        /// </summary>
        public int? ComponentCategoryId { get; set; }

        /// <summary>
        /// 特定部件ID（可选，用于专用模板）
        /// </summary>
        public int? ComponentId { get; set; }

        /// <summary>
        /// 保养类型：1-日常保养, 2-定期保养, 3-大修保养
        /// </summary>
        [Required]
        public int MaintenanceType { get; set; } = 1;

        /// <summary>
        /// 周期类型：1-按时间, 2-按使用量
        /// </summary>
        [Required]
        public int CycleType { get; set; } = 1;

        /// <summary>
        /// 时间周期（天）
        /// </summary>
        public int? CycleDays { get; set; }

        /// <summary>
        /// 使用量周期（小时）
        /// </summary>
        public int? CycleUsageHours { get; set; }

        /// <summary>
        /// 使用次数周期
        /// </summary>
        public int? CycleUsageCount { get; set; }

        /// <summary>
        /// 预计保养时长（分钟）
        /// </summary>
        [Required]
        public int EstimatedDurationMinutes { get; set; } = 60;

        /// <summary>
        /// 所需技能等级：1-初级, 2-中级, 3-高级
        /// </summary>
        [Required]
        public int RequiredSkillLevel { get; set; } = 1;

        /// <summary>
        /// 保养描述
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 保养说明
        /// </summary>
        [MaxLength(2000)]
        public string? Instructions { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(200)]
        public string? Remark { get; set; }

        // 计算属性
        /// <summary>
        /// 保养类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string MaintenanceTypeName => MaintenanceType switch
        {
            1 => "日常保养",
            2 => "定期保养",
            3 => "大修保养",
            _ => "未知"
        };

        /// <summary>
        /// 周期类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string CycleTypeName => CycleType switch
        {
            1 => "按时间",
            2 => "按使用量",
            _ => "未知"
        };

        /// <summary>
        /// 技能等级名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string SkillLevelName => RequiredSkillLevel switch
        {
            1 => "初级",
            2 => "中级",
            3 => "高级",
            _ => "未知"
        };

        /// <summary>
        /// 周期描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string CycleDescription
        {
            get
            {
                return CycleType switch
                {
                    1 => CycleDays.HasValue ? $"{CycleDays}天" : "未设置",
                    2 => CycleUsageHours.HasValue ? $"{CycleUsageHours}小时" : 
                         CycleUsageCount.HasValue ? $"{CycleUsageCount}次" : "未设置",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 预计时长描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DurationDescription
        {
            get
            {
                if (EstimatedDurationMinutes < 60)
                    return $"{EstimatedDurationMinutes}分钟";
                else if (EstimatedDurationMinutes % 60 == 0)
                    return $"{EstimatedDurationMinutes / 60}小时";
                else
                    return $"{EstimatedDurationMinutes / 60}小时{EstimatedDurationMinutes % 60}分钟";
            }
        }
    }
}
