namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 保养计划DTO
    /// </summary>
    public class MaintenanceScheduleDto
    {
        /// <summary>
        /// 计划ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public int EquipmentId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; } = string.Empty;

        /// <summary>
        /// 设备编码
        /// </summary>
        public string EquipmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 模板ID
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// 保养类型
        /// </summary>
        public int MaintenanceType { get; set; }

        /// <summary>
        /// 保养类型名称
        /// </summary>
        public string MaintenanceTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 计划日期
        /// </summary>
        public DateTime ScheduledDate { get; set; }

        /// <summary>
        /// 预计时长(分钟)
        /// </summary>
        public int EstimatedDurationMinutes { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public DateTime? ActualStartTime { get; set; }

        /// <summary>
        /// 实际完成时间
        /// </summary>
        public DateTime? ActualCompletionTime { get; set; }

        /// <summary>
        /// 所需技能等级
        /// </summary>
        public int RequiredSkillLevel { get; set; }

        /// <summary>
        /// 优先级 (1-5, 1最高)
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 优先级名称
        /// </summary>
        public string PriorityName { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public MaintenanceScheduleStatus Status { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; } = string.Empty;

        /// <summary>
        /// 分配的技师ID
        /// </summary>
        public int? AssignedTechnicianId { get; set; }

        /// <summary>
        /// 分配的技师姓名
        /// </summary>
        public string? AssignedTechnicianName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 更新人姓名
        /// </summary>
        public string? UpdatedByName { get; set; }
    }

    /// <summary>
    /// 保养计划状态枚举
    /// </summary>
    public enum MaintenanceScheduleStatus
    {
        /// <summary>
        /// 已调度
        /// </summary>
        Scheduled = 1,

        /// <summary>
        /// 执行中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 4,

        /// <summary>
        /// 已延期
        /// </summary>
        Postponed = 5
    }

    /// <summary>
    /// 保养检查项目DTO
    /// </summary>
    public class MaintenanceCheckItemDto
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 模板ID
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 项目类型
        /// </summary>
        public int ItemType { get; set; }

        /// <summary>
        /// 检查方法
        /// </summary>
        public string? CheckMethod { get; set; }

        /// <summary>
        /// 标准值
        /// </summary>
        public string? StandardValue { get; set; }

        /// <summary>
        /// 实际值
        /// </summary>
        public string? ActualValue { get; set; }

        /// <summary>
        /// 容差范围
        /// </summary>
        public string? ToleranceRange { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }
    }
}
