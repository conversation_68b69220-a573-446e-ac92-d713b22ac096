using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 保养任务表
    /// </summary>
    [SugarTable("MaintenanceTasks")]
    public class MaintenanceTask
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        [Required, MaxLength(50)]
        public string TaskNumber { get; set; } = string.Empty;

        /// <summary>
        /// 设备部件关联ID
        /// </summary>
        [Required]
        public int EquipmentComponentId { get; set; }

        /// <summary>
        /// 保养模板ID
        /// </summary>
        [Required]
        public int TemplateId { get; set; }

        /// <summary>
        /// 任务类型：1-计划保养, 2-临时保养, 3-故障保养
        /// </summary>
        [Required]
        public int TaskType { get; set; } = 1;

        /// <summary>
        /// 优先级：1-紧急, 2-高, 3-中, 4-低
        /// </summary>
        [Required]
        public int Priority { get; set; } = 2;

        /// <summary>
        /// 状态：1-待分配, 2-已分配, 3-执行中, 4-已完成, 5-已取消
        /// </summary>
        [Required]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 计划开始时间
        /// </summary>
        [Required]
        public DateTime PlannedStartDate { get; set; }

        /// <summary>
        /// 计划结束时间
        /// </summary>
        [Required]
        public DateTime PlannedEndDate { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public DateTime? ActualStartDate { get; set; }

        /// <summary>
        /// 实际结束时间
        /// </summary>
        public DateTime? ActualEndDate { get; set; }

        /// <summary>
        /// 分配给用户ID
        /// </summary>
        public int? AssignedToUserId { get; set; }

        /// <summary>
        /// 分配给部门ID
        /// </summary>
        public int? AssignedToDepartmentId { get; set; }

        /// <summary>
        /// 预计时长（分钟）
        /// </summary>
        [Required]
        public int EstimatedDurationMinutes { get; set; }

        /// <summary>
        /// 实际时长（分钟）
        /// </summary>
        public int? ActualDurationMinutes { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 执行说明
        /// </summary>
        [MaxLength(2000)]
        public string? Instructions { get; set; }

        /// <summary>
        /// 完成说明
        /// </summary>
        [MaxLength(1000)]
        public string? CompletionNotes { get; set; }

        /// <summary>
        /// 质量评分（1-5分）
        /// </summary>
        public int? QualityRating { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(200)]
        public string? Remark { get; set; }

        // 计算属性
        /// <summary>
        /// 任务类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string TaskTypeName => TaskType switch
        {
            1 => "计划保养",
            2 => "临时保养",
            3 => "故障保养",
            _ => "未知"
        };

        /// <summary>
        /// 优先级名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PriorityName => Priority switch
        {
            1 => "紧急",
            2 => "高",
            3 => "中",
            4 => "低",
            _ => "未知"
        };

        /// <summary>
        /// 状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "待分配",
            2 => "已分配",
            3 => "执行中",
            4 => "已完成",
            5 => "已取消",
            _ => "未知"
        };

        /// <summary>
        /// 优先级颜色
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PriorityColor => Priority switch
        {
            1 => "error",    // 紧急 - 红色
            2 => "warning",  // 高 - 橙色
            3 => "info",     // 中 - 蓝色
            4 => "success",  // 低 - 绿色
            _ => "default"
        };

        /// <summary>
        /// 状态颜色
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusColor => Status switch
        {
            1 => "default",  // 待分配 - 灰色
            2 => "info",     // 已分配 - 蓝色
            3 => "warning",  // 执行中 - 橙色
            4 => "success",  // 已完成 - 绿色
            5 => "error",    // 已取消 - 红色
            _ => "default"
        };

        /// <summary>
        /// 是否逾期
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsOverdue => Status != 4 && Status != 5 && PlannedEndDate < DateTime.Now;

        /// <summary>
        /// 是否即将到期（24小时内）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsDueSoon => Status != 4 && Status != 5 && 
                                PlannedEndDate > DateTime.Now && 
                                PlannedEndDate <= DateTime.Now.AddHours(24);

        /// <summary>
        /// 实际耗时描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ActualDurationDescription
        {
            get
            {
                if (!ActualDurationMinutes.HasValue)
                    return "未完成";

                var minutes = ActualDurationMinutes.Value;
                if (minutes < 60)
                    return $"{minutes}分钟";
                else if (minutes % 60 == 0)
                    return $"{minutes / 60}小时";
                else
                    return $"{minutes / 60}小时{minutes % 60}分钟";
            }
        }

        /// <summary>
        /// 质量评分描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string QualityRatingDescription => QualityRating switch
        {
            1 => "很差",
            2 => "较差",
            3 => "一般",
            4 => "良好",
            5 => "优秀",
            _ => "未评价"
        };
    }
}
