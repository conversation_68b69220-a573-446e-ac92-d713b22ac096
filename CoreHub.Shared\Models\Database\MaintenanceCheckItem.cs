using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 保养检查项目表
    /// </summary>
    [SugarTable("MaintenanceCheckItems")]
    public class MaintenanceCheckItem
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 保养模板ID
        /// </summary>
        [Required]
        public int TemplateId { get; set; }

        /// <summary>
        /// 检查项目名称
        /// </summary>
        [Required, MaxLength(100)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 项目类型：1-检查, 2-清洁, 3-润滑, 4-调整, 5-更换
        /// </summary>
        [Required]
        public int ItemType { get; set; } = 1;

        /// <summary>
        /// 检查方法
        /// </summary>
        [MaxLength(200)]
        public string? CheckMethod { get; set; }

        /// <summary>
        /// 标准值
        /// </summary>
        [MaxLength(100)]
        public string? StandardValue { get; set; }

        /// <summary>
        /// 允许范围
        /// </summary>
        [MaxLength(100)]
        public string? ToleranceRange { get; set; }

        /// <summary>
        /// 是否必检项目
        /// </summary>
        [Required]
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// 排序号
        /// </summary>
        [Required]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 项目描述
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(200)]
        public string? Remark { get; set; }

        // 计算属性
        /// <summary>
        /// 项目类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ItemTypeName => ItemType switch
        {
            1 => "检查",
            2 => "清洁",
            3 => "润滑",
            4 => "调整",
            5 => "更换",
            _ => "未知"
        };

        /// <summary>
        /// 项目类型颜色
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ItemTypeColor => ItemType switch
        {
            1 => "info",     // 检查 - 蓝色
            2 => "success",  // 清洁 - 绿色
            3 => "warning",  // 润滑 - 橙色
            4 => "secondary", // 调整 - 灰色
            5 => "error",    // 更换 - 红色
            _ => "default"
        };

        /// <summary>
        /// 项目类型图标
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ItemTypeIcon => ItemType switch
        {
            1 => "Icons.Material.Filled.Search",      // 检查
            2 => "Icons.Material.Filled.CleaningServices", // 清洁
            3 => "Icons.Material.Filled.Opacity",     // 润滑
            4 => "Icons.Material.Filled.Tune",        // 调整
            5 => "Icons.Material.Filled.ChangeCircle", // 更换
            _ => "Icons.Material.Filled.Help"
        };
    }
}
