namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 保养模板DTO
    /// </summary>
    public class MaintenanceTemplateDto
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类ID
        /// </summary>
        public int? ComponentCategoryId { get; set; }

        /// <summary>
        /// 部件分类名称
        /// </summary>
        public string? ComponentCategoryName { get; set; }

        /// <summary>
        /// 特定部件ID
        /// </summary>
        public int? ComponentId { get; set; }

        /// <summary>
        /// 特定部件名称
        /// </summary>
        public string? ComponentName { get; set; }

        /// <summary>
        /// 保养类型
        /// </summary>
        public int MaintenanceType { get; set; }

        /// <summary>
        /// 保养类型名称
        /// </summary>
        public string MaintenanceTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 周期类型
        /// </summary>
        public int CycleType { get; set; }

        /// <summary>
        /// 周期类型名称
        /// </summary>
        public string CycleTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 时间周期（天）
        /// </summary>
        public int? CycleDays { get; set; }

        /// <summary>
        /// 使用量周期（小时）
        /// </summary>
        public int? CycleUsageHours { get; set; }

        /// <summary>
        /// 使用次数周期
        /// </summary>
        public int? CycleUsageCount { get; set; }

        /// <summary>
        /// 周期描述
        /// </summary>
        public string CycleDescription { get; set; } = string.Empty;

        /// <summary>
        /// 预计保养时长（分钟）
        /// </summary>
        public int EstimatedDurationMinutes { get; set; }

        /// <summary>
        /// 预计时长描述
        /// </summary>
        public string DurationDescription { get; set; } = string.Empty;

        /// <summary>
        /// 所需技能等级
        /// </summary>
        public int RequiredSkillLevel { get; set; }

        /// <summary>
        /// 技能等级名称
        /// </summary>
        public string SkillLevelName { get; set; } = string.Empty;

        /// <summary>
        /// 保养描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 保养说明
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// 检查项目数量
        /// </summary>
        public int CheckItemCount { get; set; }

        /// <summary>
        /// 必检项目数量
        /// </summary>
        public int RequiredItemCount { get; set; }

        /// <summary>
        /// 可选项目数量
        /// </summary>
        public int OptionalItemCount { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人姓名
        /// </summary>
        public string? UpdatedByName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 适用范围描述
        /// </summary>
        public string ApplicableScope
        {
            get
            {
                if (!string.IsNullOrEmpty(ComponentName))
                    return $"专用于: {ComponentName}";
                else if (!string.IsNullOrEmpty(ComponentCategoryName))
                    return $"适用于: {ComponentCategoryName}类部件";
                else
                    return "通用模板";
            }
        }

        /// <summary>
        /// 保养类型颜色
        /// </summary>
        public string MaintenanceTypeColor { get; set; } = string.Empty;

        /// <summary>
        /// 技能等级颜色
        /// </summary>
        public string SkillLevelColor { get; set; } = string.Empty;
    }

    /// <summary>
    /// 保养模板创建/编辑DTO
    /// </summary>
    public class MaintenanceTemplateCreateDto
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类ID
        /// </summary>
        public int? ComponentCategoryId { get; set; }

        /// <summary>
        /// 特定部件ID
        /// </summary>
        public int? ComponentId { get; set; }

        /// <summary>
        /// 保养类型
        /// </summary>
        public int MaintenanceType { get; set; } = 1;

        /// <summary>
        /// 周期类型
        /// </summary>
        public int CycleType { get; set; } = 1;

        /// <summary>
        /// 时间周期（天）
        /// </summary>
        public int? CycleDays { get; set; }

        /// <summary>
        /// 使用量周期（小时）
        /// </summary>
        public int? CycleUsageHours { get; set; }

        /// <summary>
        /// 使用次数周期
        /// </summary>
        public int? CycleUsageCount { get; set; }

        /// <summary>
        /// 预计保养时长（分钟）
        /// </summary>
        public int EstimatedDurationMinutes { get; set; } = 60;

        /// <summary>
        /// 所需技能等级
        /// </summary>
        public int RequiredSkillLevel { get; set; } = 1;

        /// <summary>
        /// 保养描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 保养说明
        /// </summary>
        public string? Instructions { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 检查项目列表
        /// </summary>
        public List<MaintenanceCheckItemCreateDto> CheckItems { get; set; } = new();
    }

    /// <summary>
    /// 保养检查项目创建DTO
    /// </summary>
    public class MaintenanceCheckItemCreateDto
    {
        /// <summary>
        /// 检查项目名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 项目类型
        /// </summary>
        public int ItemType { get; set; } = 1;

        /// <summary>
        /// 检查方法
        /// </summary>
        public string? CheckMethod { get; set; }

        /// <summary>
        /// 标准值
        /// </summary>
        public string? StandardValue { get; set; }

        /// <summary>
        /// 允许范围
        /// </summary>
        public string? ToleranceRange { get; set; }

        /// <summary>
        /// 是否必检项目
        /// </summary>
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 项目描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
