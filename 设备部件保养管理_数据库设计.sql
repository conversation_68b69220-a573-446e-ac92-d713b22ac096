-- ================================
-- 设备部件保养管理 - 数据库设计
-- ================================

-- 1. 保养模板表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MaintenanceTemplates' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MaintenanceTemplates](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,                    -- 模板名称
        [ComponentCategoryId] [int] NULL,                   -- 部件分类ID（可选，用于通用模板）
        [ComponentId] [int] NULL,                           -- 特定部件ID（可选，用于专用模板）
        [MaintenanceType] [int] NOT NULL DEFAULT(1),        -- 保养类型：1-日常保养, 2-定期保养, 3-大修保养
        [CycleType] [int] NOT NULL DEFAULT(1),              -- 周期类型：1-按时间, 2-按使用量
        [CycleDays] [int] NULL,                             -- 时间周期（天）
        [CycleUsageHours] [int] NULL,                       -- 使用量周期（小时）
        [CycleUsageCount] [int] NULL,                       -- 使用次数周期
        [EstimatedDurationMinutes] [int] NOT NULL DEFAULT(60), -- 预计保养时长（分钟）
        [RequiredSkillLevel] [int] NOT NULL DEFAULT(1),     -- 所需技能等级：1-初级, 2-中级, 3-高级
        [Description] [nvarchar](500) NULL,                 -- 保养描述
        [Instructions] [nvarchar](2000) NULL,               -- 保养说明
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](200) NULL,
        CONSTRAINT [PK_MaintenanceTemplates] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MaintenanceTemplates_ComponentCategory] FOREIGN KEY([ComponentCategoryId]) REFERENCES [dbo].[ComponentCategories] ([Id]),
        CONSTRAINT [FK_MaintenanceTemplates_Component] FOREIGN KEY([ComponentId]) REFERENCES [dbo].[Components] ([Id])
    )
    
    CREATE NONCLUSTERED INDEX [IX_MaintenanceTemplates_ComponentCategory] ON [dbo].[MaintenanceTemplates] ([ComponentCategoryId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceTemplates_Component] ON [dbo].[MaintenanceTemplates] ([ComponentId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceTemplates_Type] ON [dbo].[MaintenanceTemplates] ([MaintenanceType])
    
    PRINT '保养模板表创建成功'
END
GO

-- 2. 保养检查项目表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MaintenanceCheckItems' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MaintenanceCheckItems](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [TemplateId] [int] NOT NULL,                        -- 保养模板ID
        [ItemName] [nvarchar](100) NOT NULL,                -- 检查项目名称
        [ItemType] [int] NOT NULL DEFAULT(1),               -- 项目类型：1-检查, 2-清洁, 3-润滑, 4-调整, 5-更换
        [CheckMethod] [nvarchar](200) NULL,                 -- 检查方法
        [StandardValue] [nvarchar](100) NULL,               -- 标准值
        [ToleranceRange] [nvarchar](100) NULL,              -- 允许范围
        [IsRequired] [bit] NOT NULL DEFAULT(1),             -- 是否必检项目
        [SortOrder] [int] NOT NULL DEFAULT(0),              -- 排序号
        [Description] [nvarchar](500) NULL,                 -- 项目描述
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](200) NULL,
        CONSTRAINT [PK_MaintenanceCheckItems] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MaintenanceCheckItems_Template] FOREIGN KEY([TemplateId]) REFERENCES [dbo].[MaintenanceTemplates] ([Id])
    )
    
    CREATE NONCLUSTERED INDEX [IX_MaintenanceCheckItems_Template] ON [dbo].[MaintenanceCheckItems] ([TemplateId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceCheckItems_Type] ON [dbo].[MaintenanceCheckItems] ([ItemType])
    
    PRINT '保养检查项目表创建成功'
END
GO

-- 3. 保养任务表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MaintenanceTasks' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MaintenanceTasks](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [TaskNumber] [nvarchar](50) NOT NULL,               -- 任务编号
        [EquipmentComponentId] [int] NOT NULL,              -- 设备部件关联ID
        [TemplateId] [int] NOT NULL,                        -- 保养模板ID
        [TaskType] [int] NOT NULL DEFAULT(1),               -- 任务类型：1-计划保养, 2-临时保养, 3-故障保养
        [Priority] [int] NOT NULL DEFAULT(2),               -- 优先级：1-紧急, 2-高, 3-中, 4-低
        [Status] [int] NOT NULL DEFAULT(1),                 -- 状态：1-待分配, 2-已分配, 3-执行中, 4-已完成, 5-已取消
        [PlannedStartDate] [datetime2] NOT NULL,            -- 计划开始时间
        [PlannedEndDate] [datetime2] NOT NULL,              -- 计划结束时间
        [ActualStartDate] [datetime2] NULL,                 -- 实际开始时间
        [ActualEndDate] [datetime2] NULL,                   -- 实际结束时间
        [AssignedToUserId] [int] NULL,                      -- 分配给用户ID
        [AssignedToDepartmentId] [int] NULL,                -- 分配给部门ID
        [EstimatedDurationMinutes] [int] NOT NULL,          -- 预计时长（分钟）
        [ActualDurationMinutes] [int] NULL,                 -- 实际时长（分钟）
        [Description] [nvarchar](500) NULL,                 -- 任务描述
        [Instructions] [nvarchar](2000) NULL,               -- 执行说明
        [CompletionNotes] [nvarchar](1000) NULL,            -- 完成说明
        [QualityRating] [int] NULL,                         -- 质量评分（1-5分）
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](200) NULL,
        CONSTRAINT [PK_MaintenanceTasks] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MaintenanceTasks_EquipmentComponent] FOREIGN KEY([EquipmentComponentId]) REFERENCES [dbo].[EquipmentComponents] ([Id]),
        CONSTRAINT [FK_MaintenanceTasks_Template] FOREIGN KEY([TemplateId]) REFERENCES [dbo].[MaintenanceTemplates] ([Id]),
        CONSTRAINT [FK_MaintenanceTasks_AssignedUser] FOREIGN KEY([AssignedToUserId]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_MaintenanceTasks_AssignedDepartment] FOREIGN KEY([AssignedToDepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [UK_MaintenanceTasks_TaskNumber] UNIQUE([TaskNumber])
    )
    
    CREATE NONCLUSTERED INDEX [IX_MaintenanceTasks_EquipmentComponent] ON [dbo].[MaintenanceTasks] ([EquipmentComponentId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceTasks_Status] ON [dbo].[MaintenanceTasks] ([Status])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceTasks_PlannedDate] ON [dbo].[MaintenanceTasks] ([PlannedStartDate])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceTasks_AssignedUser] ON [dbo].[MaintenanceTasks] ([AssignedToUserId])
    
    PRINT '保养任务表创建成功'
END
GO

-- 4. 保养执行记录表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MaintenanceRecords' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MaintenanceRecords](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [TaskId] [int] NOT NULL,                            -- 保养任务ID
        [CheckItemId] [int] NOT NULL,                       -- 检查项目ID
        [CheckResult] [int] NOT NULL DEFAULT(1),            -- 检查结果：1-正常, 2-异常, 3-需要关注, 4-跳过
        [ActualValue] [nvarchar](100) NULL,                 -- 实际测量值
        [IsWithinStandard] [bit] NULL,                      -- 是否在标准范围内
        [Notes] [nvarchar](500) NULL,                       -- 检查说明
        [PhotoUrls] [nvarchar](1000) NULL,                  -- 照片URL（多个用逗号分隔）
        [ExecutedBy] [int] NOT NULL,                        -- 执行人ID
        [ExecutedAt] [datetime2] NOT NULL DEFAULT(GETDATE()), -- 执行时间
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](200) NULL,
        CONSTRAINT [PK_MaintenanceRecords] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MaintenanceRecords_Task] FOREIGN KEY([TaskId]) REFERENCES [dbo].[MaintenanceTasks] ([Id]),
        CONSTRAINT [FK_MaintenanceRecords_CheckItem] FOREIGN KEY([CheckItemId]) REFERENCES [dbo].[MaintenanceCheckItems] ([Id]),
        CONSTRAINT [FK_MaintenanceRecords_ExecutedBy] FOREIGN KEY([ExecutedBy]) REFERENCES [dbo].[Users] ([Id])
    )
    
    CREATE NONCLUSTERED INDEX [IX_MaintenanceRecords_Task] ON [dbo].[MaintenanceRecords] ([TaskId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceRecords_CheckItem] ON [dbo].[MaintenanceRecords] ([CheckItemId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceRecords_ExecutedBy] ON [dbo].[MaintenanceRecords] ([ExecutedBy])
    
    PRINT '保养执行记录表创建成功'
END
GO

-- 5. 保养耗材记录表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MaintenanceMaterials' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MaintenanceMaterials](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [TaskId] [int] NOT NULL,                            -- 保养任务ID
        [ComponentId] [int] NOT NULL,                       -- 耗材部件ID
        [UsedQuantity] [decimal](10,2) NOT NULL,            -- 使用数量
        [UnitPrice] [decimal](10,2) NULL,                   -- 单价
        [TotalCost] [decimal](10,2) NULL,                   -- 总成本
        [SupplierInfo] [nvarchar](200) NULL,                -- 供应商信息
        [BatchNumber] [nvarchar](50) NULL,                  -- 批次号
        [UsedBy] [int] NOT NULL,                            -- 使用人ID
        [UsedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),   -- 使用时间
        [Notes] [nvarchar](500) NULL,                       -- 使用说明
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](200) NULL,
        CONSTRAINT [PK_MaintenanceMaterials] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MaintenanceMaterials_Task] FOREIGN KEY([TaskId]) REFERENCES [dbo].[MaintenanceTasks] ([Id]),
        CONSTRAINT [FK_MaintenanceMaterials_Component] FOREIGN KEY([ComponentId]) REFERENCES [dbo].[Components] ([Id]),
        CONSTRAINT [FK_MaintenanceMaterials_UsedBy] FOREIGN KEY([UsedBy]) REFERENCES [dbo].[Users] ([Id])
    )
    
    CREATE NONCLUSTERED INDEX [IX_MaintenanceMaterials_Task] ON [dbo].[MaintenanceMaterials] ([TaskId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceMaterials_Component] ON [dbo].[MaintenanceMaterials] ([ComponentId])
    CREATE NONCLUSTERED INDEX [IX_MaintenanceMaterials_UsedBy] ON [dbo].[MaintenanceMaterials] ([UsedBy])
    
    PRINT '保养耗材记录表创建成功'
END
GO

PRINT '设备部件保养管理数据库设计完成！'
