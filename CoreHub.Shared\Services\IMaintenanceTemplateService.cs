using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 保养模板服务接口
    /// </summary>
    public interface IMaintenanceTemplateService
    {
        /// <summary>
        /// 获取保养模板列表
        /// </summary>
        /// <param name="keyword">关键词搜索</param>
        /// <param name="componentCategoryId">部件分类ID</param>
        /// <param name="maintenanceType">保养类型</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>保养模板列表</returns>
        Task<List<MaintenanceTemplateDto>> GetMaintenanceTemplatesAsync(
            string? keyword = null,
            int? componentCategoryId = null,
            int? maintenanceType = null,
            bool? isEnabled = null);

        /// <summary>
        /// 根据ID获取保养模板详情
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>保养模板详情</returns>
        Task<MaintenanceTemplateDto?> GetMaintenanceTemplateByIdAsync(int id);

        /// <summary>
        /// 创建保养模板
        /// </summary>
        /// <param name="createDto">创建DTO</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? TemplateId)> CreateMaintenanceTemplateAsync(MaintenanceTemplateCreateDto createDto);

        /// <summary>
        /// 更新保养模板
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <param name="updateDto">更新DTO</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateMaintenanceTemplateAsync(int id, MaintenanceTemplateCreateDto updateDto);

        /// <summary>
        /// 删除保养模板
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <returns>删除结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteMaintenanceTemplateAsync(int id);

        /// <summary>
        /// 启用/禁用保养模板
        /// </summary>
        /// <param name="id">模板ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleMaintenanceTemplateStatusAsync(int id, bool isEnabled);

        /// <summary>
        /// 复制保养模板
        /// </summary>
        /// <param name="sourceId">源模板ID</param>
        /// <param name="newName">新模板名称</param>
        /// <returns>复制结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? NewTemplateId)> CopyMaintenanceTemplateAsync(int sourceId, string newName);

        /// <summary>
        /// 获取模板的检查项目
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <returns>检查项目列表</returns>
        Task<List<MaintenanceCheckItemDto>> GetTemplateCheckItemsAsync(int templateId);

        /// <summary>
        /// 为模板添加检查项目
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="checkItem">检查项目</param>
        /// <returns>添加结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? ItemId)> AddCheckItemToTemplateAsync(int templateId, MaintenanceCheckItemCreateDto checkItem);

        /// <summary>
        /// 更新检查项目
        /// </summary>
        /// <param name="itemId">项目ID</param>
        /// <param name="checkItem">检查项目</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateCheckItemAsync(int itemId, MaintenanceCheckItemCreateDto checkItem);

        /// <summary>
        /// 删除检查项目
        /// </summary>
        /// <param name="itemId">项目ID</param>
        /// <returns>删除结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteCheckItemAsync(int itemId);

        /// <summary>
        /// 批量更新检查项目排序
        /// </summary>
        /// <param name="templateId">模板ID</param>
        /// <param name="itemOrders">项目ID和排序号的映射</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateCheckItemOrdersAsync(int templateId, Dictionary<int, int> itemOrders);

        /// <summary>
        /// 获取适用于指定部件的模板
        /// </summary>
        /// <param name="componentId">部件ID</param>
        /// <param name="componentCategoryId">部件分类ID</param>
        /// <returns>适用的模板列表</returns>
        Task<List<MaintenanceTemplateDto>> GetApplicableTemplatesAsync(int? componentId = null, int? componentCategoryId = null);

        /// <summary>
        /// 验证模板配置
        /// </summary>
        /// <param name="createDto">模板配置</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, List<string> ErrorMessages)> ValidateTemplateAsync(MaintenanceTemplateCreateDto createDto);

        /// <summary>
        /// 获取保养模板统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<MaintenanceTemplateStatisticsDto> GetTemplateStatisticsAsync();
    }



    /// <summary>
    /// 保养模板统计DTO
    /// </summary>
    public class MaintenanceTemplateStatisticsDto
    {
        /// <summary>
        /// 总模板数
        /// </summary>
        public int TotalTemplates { get; set; }

        /// <summary>
        /// 启用的模板数
        /// </summary>
        public int EnabledTemplates { get; set; }

        /// <summary>
        /// 禁用的模板数
        /// </summary>
        public int DisabledTemplates { get; set; }

        /// <summary>
        /// 日常保养模板数
        /// </summary>
        public int DailyMaintenanceTemplates { get; set; }

        /// <summary>
        /// 定期保养模板数
        /// </summary>
        public int PeriodicMaintenanceTemplates { get; set; }

        /// <summary>
        /// 大修保养模板数
        /// </summary>
        public int OverhaulMaintenanceTemplates { get; set; }

        /// <summary>
        /// 通用模板数
        /// </summary>
        public int GeneralTemplates { get; set; }

        /// <summary>
        /// 专用模板数
        /// </summary>
        public int SpecificTemplates { get; set; }

        /// <summary>
        /// 平均检查项目数
        /// </summary>
        public double AverageCheckItems { get; set; }

        /// <summary>
        /// 平均预计时长（分钟）
        /// </summary>
        public double AverageDurationMinutes { get; set; }
    }
}
