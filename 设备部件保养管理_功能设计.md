# 设备部件保养管理功能设计

## 📋 功能模块概览

### 1. 保养模板管理
**页面路径**: `/maintenance-templates`
**功能描述**: 管理保养模板，定义标准保养流程

#### 主要功能
- ✅ **模板列表**: 显示所有保养模板，支持筛选和搜索
- ✅ **模板创建**: 创建新的保养模板
- ✅ **模板编辑**: 修改现有保养模板
- ✅ **检查项目管理**: 为模板添加/编辑检查项目
- ✅ **模板复制**: 基于现有模板创建新模板
- ✅ **模板启用/禁用**: 控制模板的使用状态

#### 界面设计
```
┌─────────────────────────────────────────────────────────────┐
│ 🔧 保养模板管理                                              │
├─────────────────────────────────────────────────────────────┤
│ [新建模板] [批量操作] [导入] [导出]                          │
│                                                             │
│ 筛选: [部件分类▼] [保养类型▼] [状态▼] [搜索框]              │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 模板名称 │ 适用部件 │ 保养类型 │ 周期 │ 状态 │ 操作     │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ 电机日常保养 │ 电机 │ 日常保养 │ 7天 │ 启用 │ [编辑][复制] │ │
│ │ 轴承定期保养 │ 轴承 │ 定期保养 │ 30天 │ 启用 │ [编辑][复制] │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 保养任务管理
**页面路径**: `/maintenance-tasks`
**功能描述**: 管理保养任务的创建、分配和执行

#### 主要功能
- ✅ **任务看板**: 按状态分组显示任务（待分配/已分配/执行中/已完成）
- ✅ **任务列表**: 表格形式显示所有任务
- ✅ **任务创建**: 手动创建保养任务
- ✅ **任务分配**: 将任务分配给维修人员
- ✅ **任务执行**: 执行保养任务，记录检查结果
- ✅ **任务完成**: 完成任务并评价质量

#### 界面设计
```
┌─────────────────────────────────────────────────────────────┐
│ 📅 保养任务管理                                              │
├─────────────────────────────────────────────────────────────┤
│ [新建任务] [批量分配] [自动生成] [日历视图] [看板视图] [列表视图] │
│                                                             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐             │
│ │待分配(5)│ │已分配(8)│ │执行中(3)│ │已完成(12)│             │
│ ├─────────┤ ├─────────┤ ├─────────┤ ├─────────┤             │
│ │任务001  │ │任务002  │ │任务003  │ │任务004  │             │
│ │电机保养 │ │轴承检查 │ │清洁维护 │ │润滑保养 │             │
│ │紧急     │ │高优先级 │ │中优先级 │ │已评价   │             │
│ │[分配]   │ │[查看]   │ │[完成]   │ │[查看]   │             │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘             │
└─────────────────────────────────────────────────────────────┘
```

### 3. 保养执行界面
**页面路径**: `/maintenance-execution/{taskId}`
**功能描述**: 执行具体的保养任务，记录检查结果

#### 主要功能
- ✅ **任务信息显示**: 显示任务基本信息和设备信息
- ✅ **检查项目列表**: 逐项执行检查项目
- ✅ **结果记录**: 记录检查结果、测量值、照片
- ✅ **异常处理**: 记录发现的异常情况
- ✅ **耗材记录**: 记录使用的备件和耗材
- ✅ **任务完成**: 提交完成结果

#### 界面设计
```
┌─────────────────────────────────────────────────────────────┐
│ 🔧 保养任务执行 - 任务编号: MT202401001                      │
├─────────────────────────────────────────────────────────────┤
│ 设备: 生产线A-主电机 │ 部件: 主轴承 │ 模板: 轴承定期保养      │
│ 执行人: 张三 │ 开始时间: 2024-01-15 09:00                   │
│                                                             │
│ ┌─ 检查项目 ─────────────────────────────────────────────┐   │
│ │ ☑ 1. 外观检查 - 正常 ✓                                │   │
│ │ ☑ 2. 温度测量 - 45°C (标准: <50°C) ✓                  │   │
│ │ ☐ 3. 振动检查 - [测量中...]                           │   │
│ │ ☐ 4. 润滑油检查                                       │   │
│ │ ☐ 5. 紧固件检查                                       │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                             │
│ ┌─ 当前检查项目: 振动检查 ─────────────────────────────────┐ │
│ │ 标准值: <2mm/s │ 测量值: [1.5] mm/s                    │ │
│ │ 检查结果: ○正常 ○异常 ○需要关注                        │ │
│ │ 备注: [振动值正常，无异常声音]                          │ │
│ │ 照片: [上传照片] [📷拍照]                              │ │
│ │ [上一项] [保存] [下一项]                               │ │
│ └───────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 4. 保养计划管理
**页面路径**: `/maintenance-plans`
**功能描述**: 管理设备部件的保养计划和自动任务生成

#### 主要功能
- ✅ **计划配置**: 为设备部件配置保养计划
- ✅ **自动生成**: 根据计划自动生成保养任务
- ✅ **计划调整**: 调整保养周期和模板
- ✅ **批量设置**: 批量为同类设备设置保养计划
- ✅ **计划预览**: 预览未来的保养任务安排

### 5. 保养统计分析
**页面路径**: `/maintenance-analytics`
**功能描述**: 保养数据的统计分析和报表

#### 主要功能
- ✅ **保养效率分析**: 任务完成率、平均耗时等
- ✅ **成本统计**: 保养成本、耗材消耗统计
- ✅ **设备健康度**: 基于保养数据评估设备状态
- ✅ **趋势分析**: 故障率、保养频次趋势
- ✅ **报表导出**: 生成各类保养报表

## 🎯 核心业务流程

### 保养任务生命周期
```
1. 计划生成 → 2. 任务创建 → 3. 任务分配 → 4. 任务执行 → 5. 任务完成 → 6. 效果评估
     ↓              ↓              ↓              ↓              ↓              ↓
   自动/手动      填写详情        指定人员        记录结果        质量评价        数据分析
```

### 保养提醒机制
- **提前提醒**: 保养到期前3天提醒
- **逾期提醒**: 超期未完成的任务红色标记
- **紧急任务**: 关键设备的紧急保养任务
- **批量提醒**: 同类设备的批量保养提醒

## 🔧 技术实现要点

### 1. 自动任务生成
- 基于保养周期自动计算下次保养时间
- 支持按时间周期和使用量周期
- 考虑节假日和设备停机时间

### 2. 移动端支持
- 响应式设计，支持平板和手机
- 离线数据缓存，网络恢复后同步
- 扫码功能，快速定位设备和任务

### 3. 数据集成
- 与设备运行数据集成
- 与库存管理系统集成
- 与人员管理系统集成

### 4. 权限控制
- 保养计划管理权限
- 任务分配权限
- 任务执行权限
- 数据查看权限

## 🚀 实施建议

### 开发优先级
1. **第一阶段**: 保养模板管理 + 保养任务管理（核心功能）
2. **第二阶段**: 保养执行界面 + 基础统计（执行功能）
3. **第三阶段**: 保养计划管理 + 高级分析（优化功能）

### 技术选型
- **前端**: Blazor Server + MudBlazor UI组件
- **后端**: ASP.NET Core + SqlSugar ORM
- **数据库**: SQL Server
- **文件存储**: 本地文件系统或云存储

### 集成考虑
- **设备监控系统**: 获取设备运行数据
- **库存管理系统**: 自动扣减保养耗材
- **人员管理系统**: 获取维修人员信息
- **通知系统**: 保养提醒和任务通知

### 移动端支持
- 响应式设计，支持平板执行保养任务
- PWA支持，可离线使用
- 扫码功能，快速定位设备

这个设计涵盖了设备部件保养管理的完整流程，从计划制定到执行完成，再到数据分析，形成了一个闭环的管理体系。您觉得这个设计如何？需要调整或补充哪些功能？
