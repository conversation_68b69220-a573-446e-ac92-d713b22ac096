@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@inject IMaintenanceTemplateService TemplateService
@inject IComponentCategoryService ComponentCategoryService
@inject IComponentService ComponentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-height: 70vh; overflow-y: auto;">
            <MudForm @ref="form" @bind-IsValid="@isValid">
                <MudGrid>
                    <!-- 基本信息 -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-3">基本信息</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="templateDto.Name"
                                    Label="模板名称"
                                    Required="true"
                                    RequiredError="模板名称不能为空"
                                    MaxLength="100" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudSelect T="int" @bind-Value="templateDto.MaintenanceType"
                                 Label="保养类型"
                                 Required="true">
                            <MudSelectItem T="int" Value="1">日常保养</MudSelectItem>
                            <MudSelectItem T="int" Value="2">定期保养</MudSelectItem>
                            <MudSelectItem T="int" Value="3">大修保养</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField T="string"
                                    Label="适用部件分类"
                                    Value="@("通用模板")"
                                    ReadOnly="true"
                                    HelperText="第二阶段暂时使用通用模板" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudTextField T="string"
                                    Label="特定部件"
                                    Value="@("所有部件")"
                                    ReadOnly="true"
                                    HelperText="第二阶段暂时适用于所有部件" />
                    </MudItem>
                    
                    <!-- 保养周期设置 -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-3 mt-4">保养周期设置</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" md="4">
                        <MudSelect T="int" @bind-Value="templateDto.CycleType"
                                 Label="周期类型"
                                 Required="true">
                            <MudSelectItem T="int" Value="1">按时间</MudSelectItem>
                            <MudSelectItem T="int" Value="2">按使用量</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    @if (templateDto.CycleType == 1)
                    {
                        <MudItem xs="12" md="4">
                            <MudNumericField @bind-Value="templateDto.CycleDays"
                                           Label="保养周期（天）"
                                           Required="true"
                                           RequiredError="保养周期不能为空"
                                           Min="1" />
                        </MudItem>
                    }
                    else
                    {
                        <MudItem xs="12" md="4">
                            <MudNumericField @bind-Value="templateDto.CycleUsageHours"
                                           Label="使用小时数"
                                           Min="1" />
                        </MudItem>
                        <MudItem xs="12" md="4">
                            <MudNumericField @bind-Value="templateDto.CycleUsageCount"
                                           Label="使用次数"
                                           Min="1" />
                        </MudItem>
                    }
                    
                    <!-- 其他设置 -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-3 mt-4">其他设置</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudNumericField @bind-Value="templateDto.EstimatedDurationMinutes"
                                       Label="预计时长（分钟）"
                                       Required="true"
                                       RequiredError="预计时长不能为空"
                                       Min="1" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudSelect T="int" @bind-Value="templateDto.RequiredSkillLevel"
                                 Label="所需技能等级"
                                 Required="true">
                            <MudSelectItem T="int" Value="1">初级</MudSelectItem>
                            <MudSelectItem T="int" Value="2">中级</MudSelectItem>
                            <MudSelectItem T="int" Value="3">高级</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="templateDto.Description"
                                    Label="保养描述"
                                    Lines="3"
                                    MaxLength="500" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="templateDto.Instructions"
                                    Label="保养说明"
                                    Lines="5"
                                    MaxLength="2000" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="templateDto.Remark"
                                    Label="备注"
                                    MaxLength="200" />
                    </MudItem>
                    
                    <!-- 检查项目 -->
                    <MudItem xs="12">
                        <MudStack Direction="Row" AlignItems="AlignItems.Center" Spacing="2" Class="mt-4 mb-3">
                            <MudText Typo="Typo.h6">检查项目</MudText>
                            <MudButton Variant="Variant.Outlined"
                                     Color="Color.Primary"
                                     Size="Size.Small"
                                     StartIcon="@Icons.Material.Filled.Add"
                                     OnClick="AddCheckItem">
                                添加项目
                            </MudButton>
                        </MudStack>
                    </MudItem>
                    
                    <MudItem xs="12">
                        @if (templateDto.CheckItems.Any())
                        {
                            <MudDataGrid T="MaintenanceCheckItemCreateDto"
                                       Items="@templateDto.CheckItems"
                                       Dense="true"
                                       Hover="true">
                                <Columns>
                                    <PropertyColumn Property="x => x.ItemName" Title="项目名称" />
                                    <TemplateColumn Title="项目类型">
                                        <CellTemplate>
                                            <MudText>@GetItemTypeName(context.Item.ItemType)</MudText>
                                        </CellTemplate>
                                    </TemplateColumn>
                                    <PropertyColumn Property="x => x.StandardValue" Title="标准值" />
                                    <TemplateColumn Title="必检">
                                        <CellTemplate>
                                            <MudCheckBox @bind-Checked="context.Item.IsRequired" />
                                        </CellTemplate>
                                    </TemplateColumn>
                                    <TemplateColumn Title="操作">
                                        <CellTemplate>
                                            <MudStack Direction="Row" Spacing="1">
                                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                             Size="Size.Small"
                                                             OnClick="@(() => EditCheckItem(context.Item))" />
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                             Color="Color.Error"
                                                             Size="Size.Small"
                                                             OnClick="@(() => RemoveCheckItem(context.Item))" />
                                            </MudStack>
                                        </CellTemplate>
                                    </TemplateColumn>
                                </Columns>
                            </MudDataGrid>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info">
                                还没有添加检查项目，请点击"添加项目"按钮添加。
                            </MudAlert>
                        }
                    </MudItem>
                </MudGrid>
            </MudForm>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@(!isValid || isSaving)">
            @if (isSaving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
            }
            @(IsEditMode ? "更新" : "创建")
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public MaintenanceTemplateDto? ExistingTemplate { get; set; }
    
    private MudForm form = null!;
    private bool isValid = false;
    private bool isSaving = false;
    
    private MaintenanceTemplateCreateDto templateDto = new();
    // 暂时注释，等待实现相关DTO
    // private List<ComponentCategoryDto> componentCategories = new();
    // private List<ComponentDto> components = new();
    
    private bool IsEditMode => ExistingTemplate != null;
    
    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        
        if (IsEditMode && ExistingTemplate != null)
        {
            // 加载现有模板数据
            templateDto.Name = ExistingTemplate.Name;
            templateDto.ComponentCategoryId = ExistingTemplate.ComponentCategoryId;
            templateDto.ComponentId = ExistingTemplate.ComponentId;
            templateDto.MaintenanceType = ExistingTemplate.MaintenanceType;
            templateDto.CycleType = ExistingTemplate.CycleType;
            templateDto.CycleDays = ExistingTemplate.CycleDays;
            templateDto.CycleUsageHours = ExistingTemplate.CycleUsageHours;
            templateDto.CycleUsageCount = ExistingTemplate.CycleUsageCount;
            templateDto.EstimatedDurationMinutes = ExistingTemplate.EstimatedDurationMinutes;
            templateDto.RequiredSkillLevel = ExistingTemplate.RequiredSkillLevel;
            templateDto.Description = ExistingTemplate.Description;
            templateDto.Instructions = ExistingTemplate.Instructions;
            templateDto.Remark = ExistingTemplate.Remark;
            
            // 加载检查项目
            var checkItems = await TemplateService.GetTemplateCheckItemsAsync(ExistingTemplate.Id);
            templateDto.CheckItems = checkItems.Select(ci => new MaintenanceCheckItemCreateDto
            {
                ItemName = ci.ItemName,
                ItemType = ci.ItemType,
                CheckMethod = ci.CheckMethod,
                StandardValue = ci.StandardValue,
                ToleranceRange = ci.ToleranceRange,
                IsRequired = ci.IsRequired,
                SortOrder = ci.SortOrder,
                Description = ci.Description,
                Remark = ci.Notes
            }).ToList();
        }
    }
    
    private async Task LoadData()
    {
        try
        {
            // 加载部件分类和部件数据
            // 这里需要实现相应的服务方法
            // componentCategories = await ComponentCategoryService.GetAllAsync();
            // components = await ComponentService.GetAllAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
    }
    
    private void AddCheckItem()
    {
        templateDto.CheckItems.Add(new MaintenanceCheckItemCreateDto
        {
            ItemName = "",
            ItemType = 1,
            IsRequired = true,
            SortOrder = templateDto.CheckItems.Count + 1
        });
    }
    
    private void EditCheckItem(MaintenanceCheckItemCreateDto item)
    {
        // TODO: 打开检查项目编辑对话框
        Snackbar.Add("检查项目编辑功能开发中...", Severity.Info);
    }
    
    private void RemoveCheckItem(MaintenanceCheckItemCreateDto item)
    {
        templateDto.CheckItems.Remove(item);
    }
    
    private string GetItemTypeName(int itemType)
    {
        return itemType switch
        {
            1 => "检查",
            2 => "清洁",
            3 => "润滑",
            4 => "调整",
            5 => "更换",
            _ => "未知"
        };
    }
    
    private async Task Submit()
    {
        if (!isValid) return;
        
        isSaving = true;
        try
        {
            if (IsEditMode && ExistingTemplate != null)
            {
                var result = await TemplateService.UpdateMaintenanceTemplateAsync(ExistingTemplate.Id, templateDto);
                if (result.IsSuccess)
                {
                    Snackbar.Add("模板更新成功", Severity.Success);
                    MudDialog.Close(DialogResult.Ok(true));
                }
                else
                {
                    Snackbar.Add($"更新失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            else
            {
                var result = await TemplateService.CreateMaintenanceTemplateAsync(templateDto);
                if (result.IsSuccess)
                {
                    Snackbar.Add("模板创建成功", Severity.Success);
                    MudDialog.Close(DialogResult.Ok(true));
                }
                else
                {
                    Snackbar.Add($"创建失败: {result.ErrorMessage}", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSaving = false;
        }
    }
    
    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
