@page "/maintenance-templates"
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@inject IMaintenanceTemplateService TemplateService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>保养模板管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- 页面标题 -->
    <MudStack Direction="Row" AlignItems="AlignItems.Center" Spacing="2" Class="mb-4">
        <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Large" Color="Color.Primary" />
        <MudText Typo="Typo.h4" Color="Color.Primary">保养模板管理</MudText>
    </MudStack>

    <!-- 操作按钮栏 -->
    <MudStack Direction="Row" Spacing="2" Class="mb-4">
        <MudButton Variant="Variant.Filled"
                 Color="Color.Primary"
                 StartIcon="@Icons.Material.Filled.Add"
                 OnClick="CreateTemplate">
            新建模板
        </MudButton>

        <MudButton Variant="Variant.Outlined"
                 Color="Color.Secondary"
                 StartIcon="@Icons.Material.Filled.Refresh"
                 OnClick="LoadData">
            刷新
        </MudButton>

        <MudButton Variant="Variant.Outlined"
                 Color="Color.Info"
                 StartIcon="@Icons.Material.Filled.Analytics"
                 OnClick="ShowStatistics">
            统计信息
        </MudButton>
    </MudStack>

    <!-- 筛选条件 -->
    <MudPaper Class="pa-4 mb-4">
        <MudGrid>
            <MudItem xs="12" md="3">
                <MudTextField @bind-Value="searchKeyword"
                            Label="搜索模板"
                            Placeholder="输入模板名称或描述"
                            Adornment="Adornment.Start"
                            AdornmentIcon="@Icons.Material.Filled.Search"
                            OnKeyPress="OnSearchKeyPress"
                            Clearable="true" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect T="int?" @bind-Value="filterMaintenanceType"
                         Label="保养类型"
                         Clearable="true">
                    <MudSelectItem T="int?" Value="@((int?)1)">日常保养</MudSelectItem>
                    <MudSelectItem T="int?" Value="@((int?)2)">定期保养</MudSelectItem>
                    <MudSelectItem T="int?" Value="@((int?)3)">大修保养</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect T="bool?" @bind-Value="filterIsEnabled"
                         Label="状态"
                         Clearable="true">
                    <MudSelectItem T="bool?" Value="@((bool?)true)">启用</MudSelectItem>
                    <MudSelectItem T="bool?" Value="@((bool?)false)">禁用</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled"
                         Color="Color.Primary"
                         StartIcon="@Icons.Material.Filled.FilterList"
                         OnClick="ApplyFilters"
                         FullWidth="true">
                    筛选
                </MudButton>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Outlined"
                         Color="Color.Secondary"
                         StartIcon="@Icons.Material.Filled.Clear"
                         OnClick="ClearFilters"
                         FullWidth="true">
                    清空
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 数据表格 -->
    <MudDataGrid T="MaintenanceTemplateDto"
                Items="@filteredTemplates"
                Loading="@loading"
                Hover="true"
                Striped="true"
                Dense="true"
                FixedHeader="true"
                Height="600px">
        <Columns>
            <PropertyColumn Property="x => x.Name" Title="模板名称" />
            <TemplateColumn Title="适用范围">
                <CellTemplate>
                    <MudText Typo="Typo.body2">@context.Item.ApplicableScope</MudText>
                </CellTemplate>
            </TemplateColumn>
            <TemplateColumn Title="保养类型">
                <CellTemplate>
                    <MudChip Color="@GetMaintenanceTypeColor(context.Item.MaintenanceTypeColor)"
                           Size="Size.Small">
                        @context.Item.MaintenanceTypeName
                    </MudChip>
                </CellTemplate>
            </TemplateColumn>
            <PropertyColumn Property="x => x.CycleDescription" Title="保养周期" />
            <PropertyColumn Property="x => x.DurationDescription" Title="预计时长" />
            <TemplateColumn Title="技能要求">
                <CellTemplate>
                    <MudChip Color="@GetSkillLevelColor(context.Item.SkillLevelColor)"
                           Size="Size.Small">
                        @context.Item.SkillLevelName
                    </MudChip>
                </CellTemplate>
            </TemplateColumn>
            <PropertyColumn Property="x => x.CheckItemCount" Title="检查项目" />
            <TemplateColumn Title="状态" Sortable="true" SortBy="@(x => x.IsEnabled)">
                <CellTemplate>
                    <MudChip T="string"
                           Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)"
                           Size="Size.Small">
                        @(context.Item.IsEnabled ? "启用" : "禁用")
                    </MudChip>
                </CellTemplate>
            </TemplateColumn>
            <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                <CellTemplate>
                    <MudStack Direction="Row" Spacing="1">
                        <MudTooltip Text="查看详情">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                         Color="Color.Info"
                                         Size="Size.Small"
                                         OnClick="@(() => ViewTemplate(context.Item))" />
                        </MudTooltip>
                        <MudTooltip Text="编辑模板">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                         Color="Color.Primary"
                                         Size="Size.Small"
                                         OnClick="@(() => EditTemplate(context.Item))" />
                        </MudTooltip>
                        <MudTooltip Text="@(context.Item.IsEnabled ? "禁用" : "启用")">
                            <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)"
                                         Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)"
                                         Size="Size.Small"
                                         OnClick="@(() => ToggleStatus(context.Item))" />
                        </MudTooltip>
                        <MudTooltip Text="复制模板">
                            <MudIconButton Icon="@Icons.Material.Filled.ContentCopy"
                                         Color="Color.Secondary"
                                         Size="Size.Small"
                                         OnClick="@(() => CopyTemplate(context.Item))" />
                        </MudTooltip>
                        <MudTooltip Text="删除模板">
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                         Color="Color.Error"
                                         Size="Size.Small"
                                         OnClick="@(() => DeleteTemplate(context.Item))" />
                        </MudTooltip>
                    </MudStack>
                </CellTemplate>
            </TemplateColumn>
        </Columns>
    </MudDataGrid>


</MudContainer>

@code {
    private List<MaintenanceTemplateDto> templates = new();
    private List<MaintenanceTemplateDto> filteredTemplates = new();
    private bool loading = false;

    // 筛选条件
    private string searchKeyword = string.Empty;
    private int? filterMaintenanceType = null;
    private bool? filterIsEnabled = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        loading = true;
        try
        {
            templates = await TemplateService.GetMaintenanceTemplatesAsync();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private void ApplyFilters()
    {
        filteredTemplates = templates.Where(t =>
            (string.IsNullOrEmpty(searchKeyword) ||
             t.Name.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
             (t.Description?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ?? false)) &&
            (!filterMaintenanceType.HasValue || t.MaintenanceType == filterMaintenanceType.Value) &&
            (!filterIsEnabled.HasValue || t.IsEnabled == filterIsEnabled.Value)
        ).ToList();
    }

    private void ClearFilters()
    {
        searchKeyword = string.Empty;
        filterMaintenanceType = null;
        filterIsEnabled = null;
        ApplyFilters();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            ApplyFilters();
        }
    }

    private Color GetMaintenanceTypeColor(string colorCode)
    {
        return colorCode switch
        {
            "info" => Color.Info,
            "warning" => Color.Warning,
            "error" => Color.Error,
            _ => Color.Default
        };
    }

    private Color GetSkillLevelColor(string colorCode)
    {
        return colorCode switch
        {
            "success" => Color.Success,
            "warning" => Color.Warning,
            "error" => Color.Error,
            _ => Color.Default
        };
    }

    private async Task ToggleStatus(MaintenanceTemplateDto templateDto)
    {
        try
        {
            var newStatus = !templateDto.IsEnabled;
            var result = await TemplateService.ToggleMaintenanceTemplateStatusAsync(templateDto.Id, newStatus);

            if (result.IsSuccess)
            {
                Snackbar.Add($"模板已{(newStatus ? "启用" : "禁用")}", Severity.Success);
                await LoadData();
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task CreateTemplate()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true,
            BackdropClick = false
        };

        var dialog = await DialogService.ShowAsync<MaintenanceTemplateEditDialog>("创建保养模板", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadData();
        }
    }

    private async Task ViewTemplate(MaintenanceTemplateDto template)
    {
        try
        {
            var templateDetail = await TemplateService.GetMaintenanceTemplateByIdAsync(template.Id);
            if (templateDetail != null)
            {
                var checkItems = await TemplateService.GetTemplateCheckItemsAsync(template.Id);

                var message = $"模板详情：\n" +
                             $"名称: {templateDetail.Name}\n" +
                             $"类型: {templateDetail.MaintenanceTypeName}\n" +
                             $"周期: {templateDetail.CycleDescription}\n" +
                             $"时长: {templateDetail.DurationDescription}\n" +
                             $"技能: {templateDetail.SkillLevelName}\n" +
                             $"检查项目: {checkItems.Count}个\n" +
                             $"适用范围: {templateDetail.ApplicableScope}\n" +
                             $"描述: {templateDetail.Description ?? "无"}";

                await DialogService.ShowMessageBox("模板详情", message, "确定");
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取模板详情失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task EditTemplate(MaintenanceTemplateDto template)
    {
        var parameters = new DialogParameters
        {
            ["ExistingTemplate"] = template
        };
        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true,
            BackdropClick = false
        };

        var dialog = await DialogService.ShowAsync<MaintenanceTemplateEditDialog>("编辑保养模板", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadData();
        }
    }

    private async Task CopyTemplate(MaintenanceTemplateDto template)
    {
        var newName = $"{template.Name} - 副本";
        try
        {
            var result = await TemplateService.CopyMaintenanceTemplateAsync(template.Id, newName);
            if (result.IsSuccess)
            {
                Snackbar.Add("模板复制成功", Severity.Success);
                await LoadData();
            }
            else
            {
                Snackbar.Add($"复制失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"复制失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteTemplate(MaintenanceTemplateDto template)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除模板 '{template.Name}' 吗？\n\n此操作不可撤销！",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await TemplateService.DeleteMaintenanceTemplateAsync(template.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("删除成功", Severity.Success);
                    await LoadData();
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ShowStatistics()
    {
        try
        {
            var stats = await TemplateService.GetTemplateStatisticsAsync();
            var message = $"模板统计信息：\n" +
                         $"总数: {stats.TotalTemplates}\n" +
                         $"启用: {stats.EnabledTemplates}\n" +
                         $"禁用: {stats.DisabledTemplates}\n" +
                         $"日常保养: {stats.DailyMaintenanceTemplates}\n" +
                         $"定期保养: {stats.PeriodicMaintenanceTemplates}\n" +
                         $"大修保养: {stats.OverhaulMaintenanceTemplates}";

            await DialogService.ShowMessageBox("统计信息", message, "确定");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取统计信息失败: {ex.Message}", Severity.Error);
        }
    }
}

@code {
    // 第一阶段：基础页面，详细功能将在第二阶段实现
}
