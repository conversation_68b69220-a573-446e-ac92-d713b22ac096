@page "/maintenance-tasks"
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>保养任务管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
    <!-- 页面标题 -->
    <MudStack Direction="Row" AlignItems="AlignItems.Center" Spacing="2" Class="mb-4">
        <MudIcon Icon="@Icons.Material.Filled.Assignment" Size="Size.Large" Color="Color.Primary" />
        <MudText Typo="Typo.h4" Color="Color.Primary">保养任务管理</MudText>
    </MudStack>

    <!-- 视图切换 -->
    <MudTabs @bind-ActivePanelIndex="activeTabIndex" Class="mb-4">
        <MudTabPanel Text="看板视图" Icon="@Icons.Material.Filled.ViewKanban">
            <!-- 看板视图 -->
            <MudGrid>
                <MudItem xs="12" md="3">
                    <MudPaper Class="pa-3" Style="min-height: 400px;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="2" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.Schedule" Color="Color.Default" />
                            <MudText Typo="Typo.h6">待分配 (5)</MudText>
                        </MudStack>
                        
                        <!-- 任务卡片 -->
                        <MudStack Spacing="2">
                            <MudCard Class="pa-2">
                                <MudCardContent>
                                    <MudText Typo="Typo.subtitle2">MT202401001</MudText>
                                    <MudText Typo="Typo.body2">电机日常保养</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">生产线A-主电机</MudText>
                                    <MudChip T="string" Size="Size.Small" Color="Color.Error">紧急</MudChip>
                                </MudCardContent>
                            </MudCard>
                            
                            <MudCard Class="pa-2">
                                <MudCardContent>
                                    <MudText Typo="Typo.subtitle2">MT202401002</MudText>
                                    <MudText Typo="Typo.body2">轴承定期保养</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">生产线B-主轴承</MudText>
                                    <MudChip T="string" Size="Size.Small" Color="Color.Warning">高</MudChip>
                                </MudCardContent>
                            </MudCard>
                        </MudStack>
                    </MudPaper>
                </MudItem>
                
                <MudItem xs="12" md="3">
                    <MudPaper Class="pa-3" Style="min-height: 400px;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="2" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Color="Color.Info" />
                            <MudText Typo="Typo.h6">已分配 (8)</MudText>
                        </MudStack>
                        
                        <MudStack Spacing="2">
                            <MudCard Class="pa-2">
                                <MudCardContent>
                                    <MudText Typo="Typo.subtitle2">MT202401003</MudText>
                                    <MudText Typo="Typo.body2">清洁维护</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">包装线-传送带</MudText>
                                    <MudText Typo="Typo.caption">分配给: 张三</MudText>
                                    <MudChip T="string" Size="Size.Small" Color="Color.Info">中</MudChip>
                                </MudCardContent>
                            </MudCard>
                        </MudStack>
                    </MudPaper>
                </MudItem>
                
                <MudItem xs="12" md="3">
                    <MudPaper Class="pa-3" Style="min-height: 400px;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="2" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.PlayArrow" Color="Color.Warning" />
                            <MudText Typo="Typo.h6">执行中 (3)</MudText>
                        </MudStack>
                        
                        <MudStack Spacing="2">
                            <MudCard Class="pa-2">
                                <MudCardContent>
                                    <MudText Typo="Typo.subtitle2">MT202401004</MudText>
                                    <MudText Typo="Typo.body2">润滑保养</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">冲压机-液压系统</MudText>
                                    <MudText Typo="Typo.caption">执行人: 李四</MudText>
                                    <MudProgressLinear Value="60" Color="Color.Warning" />
                                </MudCardContent>
                            </MudCard>
                        </MudStack>
                    </MudPaper>
                </MudItem>
                
                <MudItem xs="12" md="3">
                    <MudPaper Class="pa-3" Style="min-height: 400px;">
                        <MudStack AlignItems="AlignItems.Center" Spacing="2" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" />
                            <MudText Typo="Typo.h6">已完成 (12)</MudText>
                        </MudStack>
                        
                        <MudStack Spacing="2">
                            <MudCard Class="pa-2">
                                <MudCardContent>
                                    <MudText Typo="Typo.subtitle2">MT202401005</MudText>
                                    <MudText Typo="Typo.body2">设备检查</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">焊接机-控制系统</MudText>
                                    <MudText Typo="Typo.caption">完成人: 王五</MudText>
                                    <MudRating ReadOnly="true" SelectedValue="4" Size="Size.Small" />
                                </MudCardContent>
                            </MudCard>
                        </MudStack>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudTabPanel>
        
        <MudTabPanel Text="列表视图" Icon="@Icons.Material.Filled.List">
            <!-- 操作按钮栏 -->
            <MudStack Direction="Row" Spacing="2" Class="mb-4">
                <MudButton Variant="Variant.Filled"
                         Color="Color.Primary"
                         StartIcon="@Icons.Material.Filled.Add">
                    新建任务
                </MudButton>
                
                <MudButton Variant="Variant.Outlined"
                         Color="Color.Secondary"
                         StartIcon="@Icons.Material.Filled.AutoAwesome">
                    自动生成
                </MudButton>
                
                <MudButton Variant="Variant.Outlined"
                         Color="Color.Info"
                         StartIcon="@Icons.Material.Filled.Assignment">
                    批量分配
                </MudButton>
            </MudStack>

            <!-- 筛选条件 -->
            <MudPaper Class="pa-4 mb-4">
                <MudGrid>
                    <MudItem xs="12" md="3">
                        <MudTextField T="string" Label="搜索任务"
                                    Placeholder="输入任务编号或描述"
                                    Adornment="Adornment.Start"
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Clearable="true" />
                    </MudItem>
                    <MudItem xs="12" md="2">
                        <MudSelect T="int?" Label="任务状态" Clearable="true">
                            <MudSelectItem T="int?" Value="@((int?)1)">待分配</MudSelectItem>
                            <MudSelectItem T="int?" Value="@((int?)2)">已分配</MudSelectItem>
                            <MudSelectItem T="int?" Value="@((int?)3)">执行中</MudSelectItem>
                            <MudSelectItem T="int?" Value="@((int?)4)">已完成</MudSelectItem>
                            <MudSelectItem T="int?" Value="@((int?)5)">已取消</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="12" md="2">
                        <MudSelect T="int?" Label="优先级" Clearable="true">
                            <MudSelectItem T="int?" Value="@((int?)1)">紧急</MudSelectItem>
                            <MudSelectItem T="int?" Value="@((int?)2)">高</MudSelectItem>
                            <MudSelectItem T="int?" Value="@((int?)3)">中</MudSelectItem>
                            <MudSelectItem T="int?" Value="@((int?)4)">低</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="12" md="2">
                        <MudDatePicker Label="计划日期" />
                    </MudItem>
                    <MudItem xs="12" md="3">
                        <MudStack Direction="Row" Spacing="2">
                            <MudButton Variant="Variant.Filled"
                                     Color="Color.Primary"
                                     StartIcon="@Icons.Material.Filled.FilterList"
                                     FullWidth="true">
                                筛选
                            </MudButton>
                            <MudButton Variant="Variant.Outlined"
                                     Color="Color.Secondary"
                                     StartIcon="@Icons.Material.Filled.Clear"
                                     FullWidth="true">
                                清空
                            </MudButton>
                        </MudStack>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <!-- 任务列表 -->
            <MudDataGrid T="MaintenanceTaskDto"
                        Items="@tasks"
                        Loading="@loading"
                        Hover="true"
                        Striped="true"
                        Dense="true"
                        FixedHeader="true"
                        Height="500px">
                <Columns>
                    <PropertyColumn Property="x => x.TaskNumber" Title="任务编号" />
                    <PropertyColumn Property="x => x.EquipmentName" Title="设备" />
                    <PropertyColumn Property="x => x.ComponentName" Title="部件" />
                    <PropertyColumn Property="x => x.TemplateName" Title="保养模板" />
                    <TemplateColumn Title="优先级">
                        <CellTemplate>
                            <MudChip Color="@GetPriorityColor(context.Item.PriorityColor)"
                                   Size="Size.Small">
                                @context.Item.PriorityName
                            </MudChip>
                        </CellTemplate>
                    </TemplateColumn>
                    <TemplateColumn Title="状态">
                        <CellTemplate>
                            <MudChip Color="@GetStatusColor(context.Item.StatusColor)"
                                   Size="Size.Small">
                                @context.Item.StatusName
                            </MudChip>
                        </CellTemplate>
                    </TemplateColumn>
                    <PropertyColumn Property="x => x.PlannedStartDate" Title="计划开始" Format="yyyy-MM-dd" />
                    <PropertyColumn Property="x => x.AssignedToUserName" Title="分配给" />
                    <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                        <CellTemplate>
                            <MudStack Direction="Row" Spacing="1">
                                <MudTooltip Text="查看详情">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                 Color="Color.Info"
                                                 Size="Size.Small" />
                                </MudTooltip>
                                <MudTooltip Text="分配任务">
                                    <MudIconButton Icon="@Icons.Material.Filled.PersonAdd"
                                                 Color="Color.Primary"
                                                 Size="Size.Small" />
                                </MudTooltip>
                                <MudTooltip Text="开始执行">
                                    <MudIconButton Icon="@Icons.Material.Filled.PlayArrow"
                                                 Color="Color.Success"
                                                 Size="Size.Small" />
                                </MudTooltip>
                            </MudStack>
                        </CellTemplate>
                    </TemplateColumn>
                </Columns>
            </MudDataGrid>
        </MudTabPanel>
    </MudTabs>
</MudContainer>

@code {
    private int activeTabIndex = 0;
    private bool loading = false;
    private List<MaintenanceTaskDto> tasks = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        loading = true;
        try
        {
            // 模拟数据，实际应该从服务获取
            tasks = GetSampleTasks();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private Color GetPriorityColor(string colorCode)
    {
        return colorCode switch
        {
            "error" => Color.Error,
            "warning" => Color.Warning,
            "info" => Color.Info,
            "success" => Color.Success,
            _ => Color.Default
        };
    }

    private Color GetStatusColor(string colorCode)
    {
        return colorCode switch
        {
            "default" => Color.Default,
            "info" => Color.Info,
            "warning" => Color.Warning,
            "success" => Color.Success,
            "error" => Color.Error,
            _ => Color.Default
        };
    }

    private List<MaintenanceTaskDto> GetSampleTasks()
    {
        return new List<MaintenanceTaskDto>
        {
            new MaintenanceTaskDto
            {
                Id = 1,
                TaskNumber = "MT202401001",
                EquipmentName = "生产线A-主电机",
                ComponentName = "主轴承",
                TemplateName = "轴承定期保养",
                Priority = 1,
                PriorityName = "紧急",
                PriorityColor = "error",
                Status = 1,
                StatusName = "待分配",
                StatusColor = "default",
                PlannedStartDate = DateTime.Today.AddDays(1),
                AssignedToUserName = null
            },
            new MaintenanceTaskDto
            {
                Id = 2,
                TaskNumber = "MT202401002",
                EquipmentName = "包装线-传送带",
                ComponentName = "驱动电机",
                TemplateName = "电机日常保养",
                Priority = 2,
                PriorityName = "高",
                PriorityColor = "warning",
                Status = 2,
                StatusName = "已分配",
                StatusColor = "info",
                PlannedStartDate = DateTime.Today.AddDays(2),
                AssignedToUserName = "张三"
            }
        };
    }

    // 临时DTO类，实际应该在Models中定义
    public class MaintenanceTaskDto
    {
        public int Id { get; set; }
        public string TaskNumber { get; set; } = string.Empty;
        public string EquipmentName { get; set; } = string.Empty;
        public string ComponentName { get; set; } = string.Empty;
        public string TemplateName { get; set; } = string.Empty;
        public int Priority { get; set; }
        public string PriorityName { get; set; } = string.Empty;
        public string PriorityColor { get; set; } = string.Empty;
        public int Status { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public string StatusColor { get; set; } = string.Empty;
        public DateTime PlannedStartDate { get; set; }
        public string? AssignedToUserName { get; set; }
    }
}
